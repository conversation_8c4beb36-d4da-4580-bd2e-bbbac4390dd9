{"name": "code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "format": "biome format ./src --write", "check": "biome check --write ./src", "check!": "biome check --write --unsafe --no-errors-on-unmatched ./src", "clean": "rm -rf .next .output node_modules && pnpm install && pnpm dev", "build:check": "pnpm check! && pnpm build", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:drop": "pnpm drizzle-kit drop", "db:studio": "pnpm drizzle-kit studio", "db:seed": "tsx scripts/seed.ts", "prepare": "husky"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.8", "bcryptjs": "^3.0.2", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "lucide-react": "^0.525.0", "motion": "^12.22.0", "next": "15.3.4", "next-themes": "^0.4.6", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.6.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^3.0.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "drizzle-seed": "^0.3.1", "husky": "^9.1.7", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}