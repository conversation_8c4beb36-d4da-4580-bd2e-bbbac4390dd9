import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export async function GET() {
	try {
		// Test if BetterAuth is properly configured
		const authInstance = auth;

		return NextResponse.json({
			status: "success",
			message: "BetterAuth is configured",
			config: {
				baseURL: authInstance.baseURL,
				secret: authInstance.secret ? "***configured***" : "missing",
			},
		});
	} catch (error) {
		console.error("BetterAuth configuration error:", error);
		return NextResponse.json(
			{
				status: "error",
				message: "BetterAuth configuration failed",
				error: error instanceof Error ? error.message : "Unknown error",
			},
			{ status: 500 },
		);
	}
}
