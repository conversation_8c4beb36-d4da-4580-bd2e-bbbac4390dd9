import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { env } from "@/env";

export async function GET() {
  try {
    // Test if BetterAuth is properly configured
    const authInstance = auth;

    return NextResponse.json({
      status: "success",
      message: "BetterAuth is configured",
      config: {
        baseURL: authInstance.baseURL,
        secret: authInstance.secret ? "***configured***" : "missing",
      },
      env: {
        BETTER_AUTH_SECRET: env.BETTER_AUTH_SECRET
          ? "***configured***"
          : "missing",
        BETTER_AUTH_URL: env.BETTER_AUTH_URL,
        NODE_ENV: env.NODE_ENV,
      },
    });
  } catch (error) {
    console.error("BetterAuth configuration error:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "BetterAuth configuration failed",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
