import {
	boolean,
	index,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

// BetterAuth required tables

// Sessions table - User sessions
export const sessions = pgTable(
	"sessions",
	{
		id: text("id").primaryKey(),
		userId: uuid("user_id").notNull(),
		expiresAt: timestamp("expires_at").notNull(),
		token: text("token").notNull().unique(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
		ipAddress: varchar("ip_address", { length: 45 }),
		userAgent: text("user_agent"),
	},
	(table) => ({
		userIdIdx: index("sessions_user_id_idx").on(table.userId),
		tokenIdx: index("sessions_token_idx").on(table.token),
		expiresAtIdx: index("sessions_expires_at_idx").on(table.expiresAt),
	}),
);

// Verification tokens table - Email verification, password reset, etc.
export const verificationTokens = pgTable(
	"verification_tokens",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		identifier: varchar("identifier", { length: 255 }).notNull(), // email or phone
		token: text("token").notNull().unique(),
		expires: timestamp("expires").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => ({
		identifierTokenIdx: index("verification_tokens_identifier_token_idx").on(
			table.identifier,
			table.token,
		),
		tokenIdx: index("verification_tokens_token_idx").on(table.token),
		expiresIdx: index("verification_tokens_expires_idx").on(table.expires),
	}),
);

// Accounts table - For OAuth providers (future use)
export const accounts = pgTable(
	"accounts",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id").notNull(),
		accountId: text("account_id").notNull(),
		providerId: varchar("provider_id", { length: 100 }).notNull(),
		accessToken: text("access_token"),
		refreshToken: text("refresh_token"),
		idToken: text("id_token"),
		accessTokenExpiresAt: timestamp("access_token_expires_at"),
		refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
		scope: text("scope"),
		password: text("password"), // For email/password accounts
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => ({
		userIdIdx: index("accounts_user_id_idx").on(table.userId),
		providerAccountIdx: index("accounts_provider_account_idx").on(
			table.providerId,
			table.accountId,
		),
	}),
);

// Zod schemas for validation
export const insertSessionSchema = createInsertSchema(sessions);
export const selectSessionSchema = createSelectSchema(sessions);

export const insertVerificationTokenSchema =
	createInsertSchema(verificationTokens);
export const selectVerificationTokenSchema =
	createSelectSchema(verificationTokens);

export const insertAccountSchema = createInsertSchema(accounts);
export const selectAccountSchema = createSelectSchema(accounts);

// Types
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type VerificationToken = typeof verificationTokens.$inferSelect;
export type NewVerificationToken = typeof verificationTokens.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
