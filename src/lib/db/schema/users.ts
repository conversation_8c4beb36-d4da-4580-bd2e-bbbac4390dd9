import {
  boolean,
  index,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
  varchar,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";

// Enums
export const educationalBoardEnum = pgEnum("educational_board", [
  "CBSE",
  "ICSE",
  "STATE_BOARD",
]);

export const standardEnum = pgEnum("standard", ["8th", "9th", "10th"]);

// Users table - Core authentication data (BetterAuth compatible)
export const users = pgTable(
  "users",
  {
    id: uuid("id").primaryKey(),
    name: text("name").notNull(),
    email: text("email").notNull().unique(),
    emailVerified: boolean("email_verified")
      .$defaultFn(() => false)
      .notNull(),
    image: text("image"),
    createdAt: timestamp("created_at")
      .$defaultFn(() => new Date())
      .notNull(),
    updatedAt: timestamp("updated_at")
      .$defaultFn(() => new Date())
      .notNull(),
    // Additional fields for our application
    lastLoginAt: timestamp("last_login_at"),
    isActive: boolean("is_active").default(true).notNull(),
  },
  (table) => ({
    emailIdx: index("users_email_idx").on(table.email),
    isActiveIdx: index("users_is_active_idx").on(table.isActive),
    lastLoginIdx: index("users_last_login_idx").on(table.lastLoginAt),
  })
);

// User profiles table - Extended user information
export const userProfiles = pgTable(
  "user_profiles",
  {
    id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull()
			.unique(),
    fullName: varchar("full_name", { length: 255 }).notNull(),
    standard: standardEnum("standard").notNull(),
    schoolName: varchar("school_name", { length: 255 }).notNull(),
    educationalBoard: educationalBoardEnum("educational_board").notNull(),
    interests: text("interests"), // JSON array of interests
    dislikes: text("dislikes"), // JSON array of dislikes
    profileCompleted: boolean("profile_completed").default(false).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdIdx: index("user_profiles_user_id_idx").on(table.userId),
    standardBoardIdx: index("user_profiles_standard_board_idx").on(
      table.standard,
      table.educationalBoard
    ),
    profileCompletedIdx: index("user_profiles_completed_idx").on(
      table.profileCompleted
    ),
  })
);

// Zod schemas for validation
export const insertUserSchema = createInsertSchema(users, {
  name: (schema) => schema.min(1).max(255),
  email: (schema) => schema.email(),
});

export const selectUserSchema = createSelectSchema(users);

export const insertUserProfileSchema = createInsertSchema(userProfiles, {
  fullName: (schema) => schema.min(1).max(255),
  schoolName: (schema) => schema.min(1).max(255),
  interests: (schema) => schema.optional(),
  dislikes: (schema) => schema.optional(),
});

export const selectUserProfileSchema = createSelectSchema(userProfiles);

// Types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type UserProfile = typeof userProfiles.$inferSelect;
export type NewUserProfile = typeof userProfiles.$inferInsert;
